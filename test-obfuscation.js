const LuaObfuscator = require('./src/obfuscator');

async function testObfuscation() {
    console.log('🧪 Testing Lua Obfuscation System...\n');
    
    const obfuscator = new LuaObfuscator();
    
    // Test cases
    const testCases = [
        {
            name: 'Simple Print Statement',
            code: `print("Hello, World!")`
        },
        {
            name: 'Variables and Functions',
            code: `
local x = 42
local y = "test"
local function add(a, b)
    return a + b
end
print(add(x, 10))
print(y)`
        },
        {
            name: 'Complex Logic',
            code: `
-- Complex Lua script
local data = {
    name = "User",
    age = 25,
    skills = {"lua", "javascript", "python"}
}

local function processUser(user)
    if user.age >= 18 then
        print("Adult user: " .. user.name)
        for i, skill in ipairs(user.skills) do
            print("Skill " .. i .. ": " .. skill)
        end
        return true
    else
        print("Minor user: " .. user.name)
        return false
    end
end

local result = processUser(data)
print("Processing result:", result)`
        },
        {
            name: 'Loops and Conditionals',
            code: `
for i = 1, 10 do
    if i % 2 == 0 then
        print("Even:", i)
    else
        print("Odd:", i)
    end
end

local count = 0
while count < 5 do
    count = count + 1
    print("Count:", count)
end`
        }
    ];
    
    console.log('📊 Running obfuscation tests...\n');
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`Test ${i + 1}: ${testCase.name}`);
        console.log('─'.repeat(50));
        
        try {
            const startTime = Date.now();
            const obfuscatedCode = await obfuscator.obfuscate(testCase.code);
            const endTime = Date.now();
            
            console.log(`✅ Success!`);
            console.log(`📏 Original size: ${testCase.code.length} characters`);
            console.log(`📏 Obfuscated size: ${obfuscatedCode.length} characters`);
            console.log(`📈 Size ratio: ${(obfuscatedCode.length / testCase.code.length).toFixed(2)}x`);
            console.log(`⏱️  Processing time: ${endTime - startTime}ms`);
            
            // Check if obfuscated code contains original strings (should not)
            const originalStrings = testCase.code.match(/"[^"]*"/g) || [];
            let containsOriginal = false;
            
            for (const str of originalStrings) {
                if (obfuscatedCode.includes(str)) {
                    containsOriginal = true;
                    break;
                }
            }
            
            if (!containsOriginal) {
                console.log(`🔒 String obfuscation: PASSED`);
            } else {
                console.log(`⚠️  String obfuscation: FAILED (original strings found)`);
            }
            
            // Check for obfuscation features
            const features = {
                'Anti-debug layer': obfuscatedCode.includes('Security check failed'),
                'VM decoder': obfuscatedCode.includes('local function('),
                'XOR encryption': obfuscatedCode.includes('tonumber(') && obfuscatedCode.includes('16'),
                'Base64 encoding': obfuscatedCode.includes('QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm'),
                'Variable obfuscation': /[a-zA-Z_][a-zA-Z0-9_]{10,}/.test(obfuscatedCode)
            };
            
            console.log(`🛡️  Security features:`);
            for (const [feature, present] of Object.entries(features)) {
                console.log(`   ${present ? '✅' : '❌'} ${feature}`);
            }
            
            console.log('\n📝 Obfuscated code preview (first 200 chars):');
            console.log(obfuscatedCode.substring(0, 200) + '...\n');
            
        } catch (error) {
            console.log(`❌ Failed: ${error.message}`);
        }
        
        console.log('═'.repeat(50) + '\n');
    }
    
    // Performance test
    console.log('🚀 Performance Test');
    console.log('─'.repeat(50));
    
    const performanceCode = `
-- Performance test script
local function fibonacci(n)
    if n <= 1 then
        return n
    end
    return fibonacci(n-1) + fibonacci(n-2)
end

for i = 1, 10 do
    print("Fibonacci(" .. i .. ") = " .. fibonacci(i))
end`;
    
    const iterations = 5;
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        await obfuscator.obfuscate(performanceCode);
        const endTime = Date.now();
        times.push(endTime - startTime);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`📊 Performance results (${iterations} iterations):`);
    console.log(`   Average: ${avgTime.toFixed(2)}ms`);
    console.log(`   Minimum: ${minTime}ms`);
    console.log(`   Maximum: ${maxTime}ms`);
    
    console.log('\n🎉 All tests completed!');
}

// Run tests
testObfuscation().catch(console.error);
