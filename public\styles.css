/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Background Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-input: #0f172a;
    
    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    /* Border Colors */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-focus: #2563eb;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Fonts */
    --font-mono: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

body {
    font-family: var(--font-sans);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.875rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.version {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: var(--spacing-xl);
    grid-template-areas:
        "input output"
        "controls controls"
        "stats stats";
}

/* Section Styles */
.input-section {
    grid-area: input;
    min-width: 0; /* Prevent grid overflow */
}

.output-section {
    grid-area: output;
    min-width: 0; /* Prevent grid overflow */
}

.control-panel {
    grid-area: controls;
}

.stats-panel {
    grid-area: stats;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-header i {
    color: var(--primary-color);
}

.section-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* Textarea Styles */
.textarea-container {
    position: relative;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.textarea-container textarea {
    width: 100%;
    height: 400px;
    background: var(--bg-input);
    border: none;
    padding: var(--spacing-lg);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    resize: vertical;
    outline: none;
    transition: all 0.2s ease;
    min-height: 300px;
}

.textarea-container textarea:focus {
    box-shadow: inset 0 0 0 2px var(--border-focus);
}

.textarea-container textarea::placeholder {
    color: var(--text-muted);
}

.textarea-info {
    background: var(--bg-tertiary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-secondary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--border-secondary);
    color: var(--text-primary);
}

/* Control Panel Styles */
.control-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.obfuscation-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.obfuscation-controls .btn-primary {
    font-size: 1rem;
    padding: var(--spacing-lg) var(--spacing-2xl);
    min-width: 200px;
    justify-content: center;
}

.obfuscation-options {
    display: flex;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
    justify-content: center;
}

.option-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn.loading .loading-spinner {
    display: block;
}

.btn.loading span {
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-container {
    margin-top: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #3b82f6);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Statistics Panel */
.stats-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.stats-panel h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.stats-panel h3 i {
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stat-value {
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-mono);
}

/* Footer */
.footer {
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-content p {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "input"
            "controls"
            "output"
            "stats";
    }

    .obfuscation-options {
        flex-direction: column;
        align-items: center;
    }
}

/* Fallback for browsers with poor grid support */
@supports not (display: grid) {
    .main-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xl);
    }

    .input-section,
    .output-section {
        flex: 1;
        min-height: 400px;
    }

    @media (min-width: 1025px) {
        .main-content {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .input-section,
        .output-section {
            flex: 1 1 45%;
        }

        .control-panel,
        .stats-panel {
            flex: 1 1 100%;
        }
    }
}

@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .textarea-container textarea {
        height: 200px;
        padding: var(--spacing-md);
    }
    
    .obfuscation-controls .btn-primary {
        width: 100%;
    }
}
