class LuaObfuscatorApp {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.updateInputStats();
        this.checkServerHealth();
    }

    initializeElements() {
        // Input elements
        this.inputCode = document.getElementById('inputCode');
        this.outputCode = document.getElementById('outputCode');
        this.obfuscateBtn = document.getElementById('obfuscateBtn');
        this.clearInputBtn = document.getElementById('clearInputBtn');
        this.loadExampleBtn = document.getElementById('loadExampleBtn');
        this.copyOutputBtn = document.getElementById('copyOutputBtn');
        this.downloadBtn = document.getElementById('downloadBtn');

        // Status elements
        this.statusIndicator = document.getElementById('statusIndicator');
        this.inputStats = document.getElementById('inputStats');
        this.outputStats = document.getElementById('outputStats');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');

        // Statistics elements
        this.statsPanel = document.getElementById('statsPanel');
        this.originalSize = document.getElementById('originalSize');
        this.obfuscatedSize = document.getElementById('obfuscatedSize');
        this.sizeRatio = document.getElementById('sizeRatio');
        this.processingTime = document.getElementById('processingTime');

        // Options
        this.antiDebugOption = document.getElementById('antiDebugOption');
        this.vmProtectionOption = document.getElementById('vmProtectionOption');
        this.controlFlowOption = document.getElementById('controlFlowOption');

        // Modal elements
        this.modalOverlay = document.getElementById('modalOverlay');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalBody = document.getElementById('modalBody');
        this.modalClose = document.getElementById('modalClose');

        // Toast container
        this.toastContainer = document.getElementById('toastContainer');
    }

    attachEventListeners() {
        // Main functionality
        this.obfuscateBtn.addEventListener('click', () => this.obfuscateCode());
        this.clearInputBtn.addEventListener('click', () => this.clearInput());
        this.loadExampleBtn.addEventListener('click', () => this.loadExample());
        this.copyOutputBtn.addEventListener('click', () => this.copyOutput());
        this.downloadBtn.addEventListener('click', () => this.downloadOutput());

        // Input monitoring
        this.inputCode.addEventListener('input', () => this.updateInputStats());
        this.inputCode.addEventListener('paste', () => {
            setTimeout(() => this.updateInputStats(), 10);
        });

        // Modal controls
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modalOverlay.addEventListener('click', (e) => {
            if (e.target === this.modalOverlay) {
                this.closeModal();
            }
        });

        // Footer links
        document.getElementById('aboutBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.showAboutModal();
        });

        document.getElementById('helpBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.showHelpModal();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.obfuscateCode();
                        break;
                    case 'k':
                        e.preventDefault();
                        this.clearInput();
                        break;
                }
            }
        });
    }

    updateInputStats() {
        const code = this.inputCode.value;
        const lines = code.split('\n').length;
        const characters = code.length;
        this.inputStats.textContent = `Lines: ${lines} | Characters: ${characters}`;
    }

    updateStatus(status, type = 'success') {
        const statusText = this.statusIndicator.querySelector('.status-text');
        const statusDot = this.statusIndicator.querySelector('.status-dot');
        
        statusText.textContent = status;
        
        // Update dot color based on type
        statusDot.style.background = type === 'error' ? 'var(--error-color)' : 
                                   type === 'warning' ? 'var(--warning-color)' : 
                                   'var(--success-color)';
    }

    async checkServerHealth() {
        try {
            const response = await fetch('/api/health');
            if (response.ok) {
                this.updateStatus('Ready');
            } else {
                this.updateStatus('Server Error', 'error');
            }
        } catch (error) {
            this.updateStatus('Offline', 'error');
            this.showToast('Server is not responding', 'error');
        }
    }

    async obfuscateCode() {
        const code = this.inputCode.value.trim();
        
        if (!code) {
            this.showToast('Please enter some Lua code to obfuscate', 'warning');
            this.inputCode.focus();
            return;
        }

        this.setLoadingState(true);
        this.updateStatus('Processing...', 'warning');
        
        const startTime = Date.now();

        try {
            // Simulate progress updates
            this.showProgress(0, 'Initializing obfuscation...');
            await this.delay(200);
            
            this.showProgress(20, 'Analyzing code structure...');
            await this.delay(300);
            
            this.showProgress(40, 'Applying encryption layers...');
            
            const response = await fetch('/api/obfuscate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    code,
                    options: {
                        antiDebug: this.antiDebugOption.checked,
                        vmProtection: this.vmProtectionOption.checked,
                        controlFlow: this.controlFlowOption.checked
                    }
                }),
            });

            this.showProgress(70, 'Generating virtual machine...');
            await this.delay(200);
            
            this.showProgress(90, 'Finalizing obfuscation...');
            await this.delay(200);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Obfuscation failed');
            }

            const result = await response.json();
            const processingTime = Date.now() - startTime;

            this.showProgress(100, 'Complete!');
            await this.delay(500);

            this.displayResults(result, processingTime);
            this.showToast('Code obfuscated successfully!', 'success');
            this.updateStatus('Complete');

        } catch (error) {
            console.error('Obfuscation error:', error);
            this.showToast(`Obfuscation failed: ${error.message}`, 'error');
            this.updateStatus('Error', 'error');
        } finally {
            this.setLoadingState(false);
            this.hideProgress();
        }
    }

    displayResults(result, processingTime) {
        this.outputCode.value = result.obfuscatedCode;
        this.copyOutputBtn.disabled = false;
        this.downloadBtn.disabled = false;

        // Update output stats
        const lines = result.obfuscatedCode.split('\n').length;
        this.outputStats.textContent = `Lines: ${lines} | Characters: ${result.obfuscatedSize}`;

        // Update statistics panel
        this.originalSize.textContent = this.formatBytes(result.originalSize);
        this.obfuscatedSize.textContent = this.formatBytes(result.obfuscatedSize);
        this.sizeRatio.textContent = `${result.compressionRatio}x`;
        this.processingTime.textContent = `${processingTime}ms`;
        
        this.statsPanel.style.display = 'block';
    }

    setLoadingState(loading) {
        this.obfuscateBtn.classList.toggle('loading', loading);
        this.obfuscateBtn.disabled = loading;
        
        if (loading) {
            this.obfuscateBtn.querySelector('span').style.display = 'none';
            this.loadingSpinner.style.display = 'block';
        } else {
            this.obfuscateBtn.querySelector('span').style.display = 'inline';
            this.loadingSpinner.style.display = 'none';
        }
    }

    showProgress(percentage, text) {
        this.progressContainer.style.display = 'block';
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = text;
    }

    hideProgress() {
        setTimeout(() => {
            this.progressContainer.style.display = 'none';
        }, 1000);
    }

    clearInput() {
        this.inputCode.value = '';
        this.outputCode.value = '';
        this.updateInputStats();
        this.outputStats.textContent = 'Ready for obfuscation';
        this.copyOutputBtn.disabled = true;
        this.downloadBtn.disabled = true;
        this.statsPanel.style.display = 'none';
        this.inputCode.focus();
        this.showToast('Input cleared', 'success');
    }

    loadExample() {
        const exampleCode = `-- Example Lua Script
print("Welcome to Lua Obfuscator!")

-- Variables and calculations
local name = "User"
local age = 25
local isActive = true

-- Function definition
local function greetUser(userName, userAge)
    if userAge >= 18 then
        print("Hello, " .. userName .. "! You are " .. userAge .. " years old.")
        return "adult"
    else
        print("Hi, " .. userName .. "! You are still young.")
        return "minor"
    end
end

-- Function call
local status = greetUser(name, age)

-- Loop example
for i = 1, 5 do
    print("Count: " .. i)
end

-- Table example
local data = {
    x = 10,
    y = 20,
    calculate = function(self)
        return self.x + self.y
    end
}

print("Result: " .. data:calculate())

-- Conditional logic
if status == "adult" and isActive then
    print("User is an active adult")
elseif status == "minor" then
    print("User is a minor")
else
    print("Unknown status")
end`;

        this.inputCode.value = exampleCode;
        this.updateInputStats();
        this.showToast('Example code loaded', 'success');
    }

    async copyOutput() {
        try {
            await navigator.clipboard.writeText(this.outputCode.value);
            this.showToast('Obfuscated code copied to clipboard!', 'success');
        } catch (error) {
            // Fallback for older browsers
            this.outputCode.select();
            document.execCommand('copy');
            this.showToast('Obfuscated code copied to clipboard!', 'success');
        }
    }

    downloadOutput() {
        const code = this.outputCode.value;
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'obfuscated_script.lua';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('Obfuscated script downloaded!', 'success');
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        this.toastContainer.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    showModal(title, content) {
        this.modalTitle.textContent = title;
        this.modalBody.innerHTML = content;
        this.modalOverlay.classList.add('active');
    }

    closeModal() {
        this.modalOverlay.classList.remove('active');
    }

    showAboutModal() {
        const content = `
            <div style="line-height: 1.6;">
                <h4 style="margin-bottom: 1rem; color: var(--primary-color);">Advanced Lua Script Obfuscator</h4>
                <p style="margin-bottom: 1rem;">This tool provides military-grade obfuscation for Lua scripts using multiple layers of protection:</p>
                <ul style="margin-bottom: 1rem; padding-left: 1.5rem;">
                    <li><strong>Multi-layer Encryption:</strong> Base64 encoding with custom alphabet + XOR encryption</li>
                    <li><strong>Virtual Machine Protection:</strong> Embedded VM decoder that executes obfuscated bytecode</li>
                    <li><strong>Anti-Debug Features:</strong> Protection against reverse engineering attempts</li>
                    <li><strong>Control Flow Obfuscation:</strong> Complex execution paths to confuse analyzers</li>
                </ul>
                <p style="margin-bottom: 1rem;">The obfuscated output maintains full compatibility with standard Lua interpreters while making the code extremely difficult to reverse engineer.</p>
                <p style="font-size: 0.875rem; color: var(--text-secondary);">Version 1.0.0 | Built with Node.js and advanced cryptographic techniques</p>
            </div>
        `;
        this.showModal('About Lua Obfuscator', content);
    }

    showHelpModal() {
        const content = `
            <div style="line-height: 1.6;">
                <h4 style="margin-bottom: 1rem; color: var(--primary-color);">How to Use</h4>
                <div style="margin-bottom: 1.5rem;">
                    <h5 style="margin-bottom: 0.5rem;">Step 1: Input Your Code</h5>
                    <p style="margin-bottom: 1rem; color: var(--text-secondary);">Paste or type your Lua script in the left text area. You can also click "Load Example" to see a sample script.</p>
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <h5 style="margin-bottom: 0.5rem;">Step 2: Configure Options</h5>
                    <ul style="margin-bottom: 1rem; padding-left: 1.5rem; color: var(--text-secondary);">
                        <li><strong>Anti-Debug Protection:</strong> Adds runtime checks to prevent debugging</li>
                        <li><strong>Virtual Machine Protection:</strong> Wraps code in a custom VM interpreter</li>
                        <li><strong>Control Flow Obfuscation:</strong> Scrambles execution order</li>
                    </ul>
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <h5 style="margin-bottom: 0.5rem;">Step 3: Obfuscate</h5>
                    <p style="margin-bottom: 1rem; color: var(--text-secondary);">Click the "Obfuscate Script" button to process your code. The obfuscated result will appear on the right.</p>
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <h5 style="margin-bottom: 0.5rem;">Step 4: Copy or Download</h5>
                    <p style="margin-bottom: 1rem; color: var(--text-secondary);">Use the "Copy" button to copy the result to clipboard, or "Download" to save as a .lua file.</p>
                </div>
                <div style="background: var(--bg-tertiary); padding: 1rem; border-radius: 0.5rem; margin-top: 1rem;">
                    <h5 style="margin-bottom: 0.5rem;">Keyboard Shortcuts</h5>
                    <ul style="padding-left: 1.5rem; color: var(--text-secondary); font-size: 0.875rem;">
                        <li><kbd>Ctrl+Enter</kbd> - Obfuscate code</li>
                        <li><kbd>Ctrl+K</kbd> - Clear input</li>
                    </ul>
                </div>
            </div>
        `;
        this.showModal('Help & Instructions', content);
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Add slideOut animation to CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LuaObfuscatorApp();
});
