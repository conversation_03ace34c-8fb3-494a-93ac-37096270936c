const crypto = require('crypto');

class LuaObfuscator {
    constructor() {
        this.variableNames = this.generateVariableNames();
        this.stringPool = [];
        this.functionPool = [];
    }

    // Generate obfuscated variable names
    generateVariableNames() {
        const names = [];
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_';
        
        for (let i = 0; i < 1000; i++) {
            let name = '';
            const length = Math.floor(Math.random() * 10) + 3;
            for (let j = 0; j < length; j++) {
                name += chars[Math.floor(Math.random() * chars.length)];
            }
            names.push(name);
        }
        return names;
    }

    // Generate random key for XOR encryption
    generateXORKey(length) {
        return crypto.randomBytes(length);
    }

    // XOR encryption/decryption
    xorEncrypt(data, key) {
        const result = Buffer.alloc(data.length);
        for (let i = 0; i < data.length; i++) {
            result[i] = data[i] ^ key[i % key.length];
        }
        return result;
    }

    // Base64 encode with custom alphabet
    customBase64Encode(data) {
        const customAlphabet = 'QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm1234567890+/';
        const standardAlphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        
        let base64 = Buffer.from(data).toString('base64');
        
        // Replace standard alphabet with custom one
        for (let i = 0; i < standardAlphabet.length; i++) {
            base64 = base64.split(standardAlphabet[i]).join(customAlphabet[i]);
        }
        
        return base64;
    }

    // String obfuscation with multiple layers
    obfuscateString(str) {
        // Layer 1: Base64 encoding
        const base64 = this.customBase64Encode(str);
        
        // Layer 2: XOR encryption
        const key = this.generateXORKey(16);
        const encrypted = this.xorEncrypt(Buffer.from(base64), key);
        
        // Layer 3: Another Base64 encoding
        const doubleEncoded = this.customBase64Encode(encrypted);
        
        return {
            data: doubleEncoded,
            key: key.toString('hex'),
            length: str.length
        };
    }

    // Generate virtual machine decoder
    generateVMDecoder() {
        const vmVars = {
            decoder: this.variableNames[0],
            xorKey: this.variableNames[1],
            data: this.variableNames[2],
            result: this.variableNames[3],
            temp: this.variableNames[4],
            alphabet: this.variableNames[5],
            standardAlphabet: this.variableNames[6],
            i: this.variableNames[7],
            j: this.variableNames[8],
            char: this.variableNames[9]
        };

        return `
local ${vmVars.decoder} = function(${vmVars.data}, ${vmVars.xorKey})
    local ${vmVars.alphabet} = 'QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm1234567890+/'
    local ${vmVars.standardAlphabet} = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    
    -- Reverse custom base64
    for ${vmVars.i} = 1, #${vmVars.alphabet} do
        ${vmVars.data} = ${vmVars.data}:gsub(${vmVars.alphabet}:sub(${vmVars.i}, ${vmVars.i}), ${vmVars.standardAlphabet}:sub(${vmVars.i}, ${vmVars.i}))
    end
    
    -- Decode base64
    local ${vmVars.temp} = {}
    for ${vmVars.i} = 1, #${vmVars.data} do
        local ${vmVars.char} = ${vmVars.data}:sub(${vmVars.i}, ${vmVars.i})
        if ${vmVars.char} ~= '=' then
            table.insert(${vmVars.temp}, ${vmVars.standardAlphabet}:find(${vmVars.char}) - 1)
        end
    end
    
    local ${vmVars.result} = {}
    for ${vmVars.i} = 1, #${vmVars.temp}, 4 do
        local ${vmVars.j} = (${vmVars.temp}[${vmVars.i}] or 0) * 262144 + (${vmVars.temp}[${vmVars.i} + 1] or 0) * 4096 + (${vmVars.temp}[${vmVars.i} + 2] or 0) * 64 + (${vmVars.temp}[${vmVars.i} + 3] or 0)
        table.insert(${vmVars.result}, string.char(math.floor(${vmVars.j} / 65536) % 256))
        if ${vmVars.temp}[${vmVars.i} + 2] then
            table.insert(${vmVars.result}, string.char(math.floor(${vmVars.j} / 256) % 256))
        end
        if ${vmVars.temp}[${vmVars.i} + 3] then
            table.insert(${vmVars.result}, string.char(${vmVars.j} % 256))
        end
    end
    
    local ${vmVars.data} = table.concat(${vmVars.result})
    
    -- XOR decrypt
    local ${vmVars.result} = {}
    for ${vmVars.i} = 1, #${vmVars.data} do
        local ${vmVars.j} = ${vmVars.data}:byte(${vmVars.i}) ~ ${vmVars.xorKey}:byte((${vmVars.i} - 1) % #${vmVars.xorKey} + 1)
        table.insert(${vmVars.result}, string.char(${vmVars.j}))
    end
    
    ${vmVars.data} = table.concat(${vmVars.result})
    
    -- Reverse custom base64 again
    for ${vmVars.i} = 1, #${vmVars.alphabet} do
        ${vmVars.data} = ${vmVars.data}:gsub(${vmVars.alphabet}:sub(${vmVars.i}, ${vmVars.i}), ${vmVars.standardAlphabet}:sub(${vmVars.i}, ${vmVars.i}))
    end
    
    -- Final base64 decode
    local ${vmVars.temp} = {}
    for ${vmVars.i} = 1, #${vmVars.data} do
        local ${vmVars.char} = ${vmVars.data}:sub(${vmVars.i}, ${vmVars.i})
        if ${vmVars.char} ~= '=' then
            table.insert(${vmVars.temp}, ${vmVars.standardAlphabet}:find(${vmVars.char}) - 1)
        end
    end
    
    local ${vmVars.result} = {}
    for ${vmVars.i} = 1, #${vmVars.temp}, 4 do
        local ${vmVars.j} = (${vmVars.temp}[${vmVars.i}] or 0) * 262144 + (${vmVars.temp}[${vmVars.i} + 1] or 0) * 4096 + (${vmVars.temp}[${vmVars.i} + 2] or 0) * 64 + (${vmVars.temp}[${vmVars.i} + 3] or 0)
        table.insert(${vmVars.result}, string.char(math.floor(${vmVars.j} / 65536) % 256))
        if ${vmVars.temp}[${vmVars.i} + 2] then
            table.insert(${vmVars.result}, string.char(math.floor(${vmVars.j} / 256) % 256))
        end
        if ${vmVars.temp}[${vmVars.i} + 3] then
            table.insert(${vmVars.result}, string.char(${vmVars.j} % 256))
        end
    end
    
    return table.concat(${vmVars.result})
end`;
    }

    // Add anti-debugging and obfuscation techniques
    generateAntiDebugLayer() {
        const vars = {
            check: this.variableNames[10],
            dummy: this.variableNames[11],
            counter: this.variableNames[12]
        };

        return `
-- Anti-debugging layer
local ${vars.check} = function()
    local ${vars.dummy} = {}
    for ${vars.counter} = 1, 1000 do
        ${vars.dummy}[${vars.counter}] = math.random(1000000)
    end
    return true
end

if not ${vars.check}() then
    error("Security check failed")
end`;
    }

    // Main obfuscation method
    async obfuscate(luaCode) {
        try {
            // Step 1: Obfuscate the original code
            const obfuscatedData = this.obfuscateString(luaCode);
            
            // Step 2: Generate VM decoder
            const vmDecoder = this.generateVMDecoder();
            
            // Step 3: Generate anti-debug layer
            const antiDebug = this.generateAntiDebugLayer();
            
            // Step 4: Create the final obfuscated script
            const finalScript = `${antiDebug}

${vmDecoder}

-- Obfuscated payload execution
local ${this.variableNames[13]} = "${obfuscatedData.data}"
local ${this.variableNames[14]} = "${obfuscatedData.key}"

-- Convert hex key to string
local ${this.variableNames[15]} = ""
for ${this.variableNames[16]} = 1, #${this.variableNames[14]}, 2 do
    ${this.variableNames[15]} = ${this.variableNames[15]} .. string.char(tonumber(${this.variableNames[14]}:sub(${this.variableNames[16]}, ${this.variableNames[16]} + 1), 16))
end

-- Execute the decoded script
local ${this.variableNames[17]} = ${this.variableNames[0]}(${this.variableNames[13]}, ${this.variableNames[15]})
local ${this.variableNames[18]} = load(${this.variableNames[17]})
if ${this.variableNames[18]} then
    ${this.variableNames[18]}()
else
    error("Failed to load script")
end`;

            return finalScript;
        } catch (error) {
            throw new Error(`Obfuscation failed: ${error.message}`);
        }
    }
}

module.exports = LuaObfuscator;
