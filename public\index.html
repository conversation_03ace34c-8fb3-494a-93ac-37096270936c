<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Lua Script Obfuscator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Lua Obfuscator</h1>
                </div>
                <div class="header-info">
                    <span class="version">v1.0.0</span>
                    <div class="status-indicator" id="statusIndicator">
                        <span class="status-dot"></span>
                        <span class="status-text">Ready</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Input Section -->
            <section class="input-section">
                <div class="section-header">
                    <h2><i class="fas fa-code"></i> Original Lua Script</h2>
                    <div class="section-controls">
                        <button class="btn btn-secondary" id="clearInputBtn">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                        <button class="btn btn-secondary" id="loadExampleBtn">
                            <i class="fas fa-file-code"></i> Load Example
                        </button>
                    </div>
                </div>
                <div class="textarea-container">
                    <textarea 
                        id="inputCode" 
                        placeholder="-- Enter your Lua script here...
-- Example:
print('Hello, World!')
local x = 42
local y = x * 2
print('Result:', y)"
                        spellcheck="false"
                    ></textarea>
                    <div class="textarea-info">
                        <span id="inputStats">Lines: 0 | Characters: 0</span>
                    </div>
                </div>
            </section>

            <!-- Control Panel -->
            <section class="control-panel">
                <div class="obfuscation-controls">
                    <button class="btn btn-primary" id="obfuscateBtn">
                        <i class="fas fa-lock"></i>
                        <span>Obfuscate Script</span>
                        <div class="loading-spinner" id="loadingSpinner"></div>
                    </button>
                    
                    <div class="obfuscation-options">
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="antiDebugOption" checked>
                                <span class="checkmark"></span>
                                Anti-Debug Protection
                            </label>
                        </div>
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="vmProtectionOption" checked>
                                <span class="checkmark"></span>
                                Virtual Machine Protection
                            </label>
                        </div>
                        <div class="option-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="controlFlowOption" checked>
                                <span class="checkmark"></span>
                                Control Flow Obfuscation
                            </label>
                        </div>
                    </div>
                </div>

                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Processing...</div>
                </div>
            </section>

            <!-- Output Section -->
            <section class="output-section">
                <div class="section-header">
                    <h2><i class="fas fa-shield-alt"></i> Obfuscated Script</h2>
                    <div class="section-controls">
                        <button class="btn btn-secondary" id="copyOutputBtn" disabled>
                            <i class="fas fa-copy"></i> Copy
                        </button>
                        <button class="btn btn-secondary" id="downloadBtn" disabled>
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>
                <div class="textarea-container">
                    <textarea 
                        id="outputCode" 
                        placeholder="Obfuscated code will appear here..."
                        readonly
                        spellcheck="false"
                    ></textarea>
                    <div class="textarea-info">
                        <span id="outputStats">Ready for obfuscation</span>
                    </div>
                </div>
            </section>

            <!-- Statistics Panel -->
            <section class="stats-panel" id="statsPanel" style="display: none;">
                <h3><i class="fas fa-chart-bar"></i> Obfuscation Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Original Size:</span>
                        <span class="stat-value" id="originalSize">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Obfuscated Size:</span>
                        <span class="stat-value" id="obfuscatedSize">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Size Ratio:</span>
                        <span class="stat-value" id="sizeRatio">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Processing Time:</span>
                        <span class="stat-value" id="processingTime">-</span>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2025 Advanced Lua Obfuscator. Built with multi-layer encryption and VM protection.</p>
                <div class="footer-links">
                    <a href="#" id="aboutBtn"><i class="fas fa-info-circle"></i> About</a>
                    <a href="#" id="helpBtn"><i class="fas fa-question-circle"></i> Help</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modalTitle">Modal Title</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                Modal content goes here...
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
