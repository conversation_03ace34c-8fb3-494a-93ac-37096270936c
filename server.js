const express = require('express');
const cors = require('cors');
const path = require('path');
const LuaObfuscator = require('./src/obfuscator');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Initialize obfuscator
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', async (req, res) => {
    try {
        const { code } = req.body;
        
        if (!code || typeof code !== 'string') {
            return res.status(400).json({ 
                error: 'Invalid input: code must be a non-empty string' 
            });
        }

        if (code.length > 1000000) { // 1MB limit
            return res.status(400).json({ 
                error: 'Code too large: maximum size is 1MB' 
            });
        }

        const obfuscatedCode = await obfuscator.obfuscate(code);
        
        res.json({ 
            success: true, 
            obfuscatedCode,
            originalSize: code.length,
            obfuscatedSize: obfuscatedCode.length,
            compressionRatio: (obfuscatedCode.length / code.length).toFixed(2)
        });
    } catch (error) {
        console.error('Obfuscation error:', error);
        res.status(500).json({ 
            error: 'Internal server error during obfuscation',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Lua Obfuscator Server running on http://localhost:${PORT}`);
    console.log(`📁 Serving static files from: ${path.join(__dirname, 'public')}`);
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
