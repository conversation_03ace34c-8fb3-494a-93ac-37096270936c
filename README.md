# Advanced Lua Script Obfuscator

A powerful web application for obfuscating Lua scripts with military-grade protection using multi-layer encryption, virtual machine decoding, and advanced anti-debugging techniques.

![Lua Obfuscator](https://img.shields.io/badge/Lua-Obfuscator-blue?style=for-the-badge&logo=lua)
![Node.js](https://img.shields.io/badge/Node.js-339933?style=for-the-badge&logo=nodedotjs&logoColor=white)
![Express](https://img.shields.io/badge/Express-000000?style=for-the-badge&logo=express&logoColor=white)

## 🚀 Features

### 🔒 Multi-Layer Obfuscation
- **Base64 Encoding** with custom alphabet rotation
- **XOR Encryption** with randomly generated keys
- **Double Encoding** for enhanced security
- **String Pool Obfuscation** to hide literal values

### 🛡️ Advanced Protection
- **Virtual Machine Decoder** - Embedded VM that executes obfuscated bytecode
- **Anti-Debug Layer** - Runtime checks to prevent reverse engineering
- **Control Flow Obfuscation** - Complex execution paths to confuse analyzers
- **Variable Name Mangling** - All identifiers replaced with random strings

### 🌐 Modern Web Interface
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Real-time Statistics** - Shows obfuscation metrics and performance
- **Syntax Highlighting** - Code editor with Lua syntax support
- **Copy & Download** - Easy export of obfuscated code
- **Progress Tracking** - Visual feedback during obfuscation process

## 📋 Requirements

- Node.js 14.0 or higher
- npm or yarn package manager
- Modern web browser (Chrome, Firefox, Safari, Edge)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lua-obfuscator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

## 🎯 Usage

### Web Interface

1. **Input Code**: Paste your Lua script in the left text area
2. **Configure Options**: Select desired protection features
   - Anti-Debug Protection
   - Virtual Machine Protection  
   - Control Flow Obfuscation
3. **Obfuscate**: Click the "Obfuscate Script" button
4. **Export**: Copy to clipboard or download as .lua file

### API Endpoint

```javascript
POST /api/obfuscate
Content-Type: application/json

{
  "code": "print('Hello, World!')",
  "options": {
    "antiDebug": true,
    "vmProtection": true,
    "controlFlow": true
  }
}
```

**Response:**
```javascript
{
  "success": true,
  "obfuscatedCode": "-- Obfuscated Lua code here",
  "originalSize": 22,
  "obfuscatedSize": 3337,
  "compressionRatio": "151.68"
}
```

## 🔧 Configuration

### Environment Variables

- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment mode (development/production)

### Obfuscation Options

- **Anti-Debug Protection**: Adds runtime security checks
- **VM Protection**: Wraps code in virtual machine interpreter
- **Control Flow Obfuscation**: Scrambles execution order

## 📊 Performance

Based on test results:
- **Average Processing Time**: 0.6ms per script
- **Size Increase**: 8x - 150x depending on original code size
- **Memory Usage**: Minimal overhead
- **Compatibility**: Works with all standard Lua interpreters

## 🧪 Testing

Run the test suite to validate obfuscation functionality:

```bash
node test-obfuscation.js
```

The test suite includes:
- Simple print statements
- Variables and functions
- Complex logic with loops
- Performance benchmarks

## 🏗️ Project Structure

```
lua-obfuscator/
├── public/                 # Frontend files
│   ├── index.html         # Main web interface
│   ├── styles.css         # Modern CSS styling
│   └── script.js          # Frontend JavaScript
├── src/                   # Backend source code
│   ├── obfuscator.js      # Core obfuscation engine
│   └── vm-decoder.js      # Virtual machine decoder
├── server.js              # Express.js server
├── test-obfuscation.js    # Test suite
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

## 🔐 Security Features

### Obfuscation Layers
1. **String Encoding**: Custom Base64 with alphabet rotation
2. **XOR Encryption**: Random key generation for each session
3. **VM Wrapping**: Code executed through custom interpreter
4. **Anti-Debug**: Runtime protection against analysis tools

### Protection Mechanisms
- Variable name randomization
- Control flow scrambling
- String literal hiding
- Function signature obfuscation
- Runtime integrity checks

## 🚀 Deployment

### Local Development
```bash
npm run dev
```

### Production
```bash
NODE_ENV=production npm start
```

### Docker (Optional)
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool is designed for legitimate code protection purposes. Users are responsible for ensuring compliance with applicable laws and regulations. The developers are not responsible for any misuse of this software.

## 🆘 Support

- **Issues**: Report bugs and request features via GitHub Issues
- **Documentation**: Check the built-in help system in the web interface
- **Performance**: Run `node test-obfuscation.js` for diagnostics

## 🎉 Acknowledgments

- Built with Node.js and Express.js
- Uses advanced cryptographic techniques
- Inspired by modern code protection methodologies
- Designed for maximum compatibility with Lua interpreters

---

**Made with ❤️ for the Lua community**
